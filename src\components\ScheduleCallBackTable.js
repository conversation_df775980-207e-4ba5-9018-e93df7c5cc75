import { <PERSON><PERSON>, Card, Select, Space, Spin, Table } from "antd";
import { useEffect, useState } from "react";
import openNotificationWithIcon from "./Notification";
import apiClient from "../config/apiClient";
import initializeEcho from "../config/echo";

export const ScheduleCallBackTable = ({ setNumber, setRedialClick, setDialerVisible, setRedialId }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [updatingId, setUpdatingId] = useState(null);
    const uId = sessionStorage.getItem("id");

    const [paginate, setPaginate] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    useEffect(() => {
        if (!uId) return;

        const echo = initializeEcho(sessionStorage.getItem("agent_token"));
        setLoading(true);

        const channel = echo.private(`agent-panel-getschedule-callback-report-channel.${uId}`);

        const handler = (e) => {
            try {
                const meta = e.data?.meta || {};
                const rows = e.data?.records || [];

                const map = new Map();
                for (const r of rows) {
                const k = `${r.id}-${r.category}-${r.date}`;
                map.set(k, r);
                }
                const unique = Array.from(map.values());

                const order = { today: 1, yesterday: 2, overdue: 3, upcoming: 4 };
                unique.sort((a, b) =>
                    (order[a.category] - order[b.category]) ||
                    (new Date(a.date) - new Date(b.date))
                );

                setData(unique);
                setPaginate(prev => ({
                    ...prev,
                    current: meta.current ?? prev.current,
                    pageSize: meta.pageSize ?? prev.pageSize,
                    total: meta.total ?? prev.total,
                }));
            } catch {
                openNotificationWithIcon("error", "Failed to process schedule callbacks");
                setData([]);
                setPaginate(prev => ({ ...prev, total: 0 }));
            } finally {
                setLoading(false);
            }
        };

        channel.listen(".agent-panel-getschedule-callback-report", handler);
        return () => channel.stopListening(".agent-panel-getschedule-callback-report");
    }, [uId]);

    const handleStatusChange = async (value, record) => {
        setNumber(record.number);
        setRedialClick(true);
        setDialerVisible(true);
        setRedialId(record.id);
    };

    const columnsCallBack = [
        { title: "Number", dataIndex: "number", key: "number" },
        {
            title: "Date",
            dataIndex: "date",
            key: "date",
            render: (text) => new Date(text).toLocaleString(),
        },
        {
            title: "Category",
            dataIndex: "category",
            key: "category",
            render: (val) => <span style={{ textTransform: "capitalize" }}>{val}</span>,
        },
        {
            title: "Action",
            dataIndex: "status",
            key: "status",
            render: (text, record) => (
                <Space>
                    {updatingId === record.id ? (
                        <Spin size="small" />
                    ) : (
                        <Select value={text} onChange={(val) => updateCallBackStatus(val, record.id, uId)} style={{ width: 120 }}>
                            <Select.Option value="pending">Pending</Select.Option>
                            <Select.Option value="cancelled">Cancelled</Select.Option>
                            {/* <Select.Option value="completed">Completed</Select.Option> */}
                            {/* <Select.Option value="redial">Redial</Select.Option> */}
                        </Select>
                    )}
                    <Button onClick={() => handleStatusChange("redial", record)}>Call now</Button>
                </Space>
            ),
        },
    ];

    const handlePaginationChange = (tableKey) => (page, pageSize) => {
        apiClient.post('/api/update-pagination', {
            table: tableKey,
            current: page,
            pageSize: pageSize
        }).finally(() => {
            setPaginate(prev => ({ ...prev, current: page, pageSize }));
        });
    };

    const updateCallBackStatus = (status, callbackId, uId) => {
        apiClient.patch("/api/callback/status", {
            id: callbackId,
            agent: uId,
            status: status,
        })
        .then((response) => {
            if (response?.data?.message === "Status updated successfully") {
                openNotificationWithIcon("success", "Status updated successfully");
            }
        })
        .catch((error) => {
            openNotificationWithIcon("error", "Failed to update schedule callbacks status.");
        });
    };

    return (
        <>
            <br />
            <Card title="Schedule Callback Requests">
                <Table
                    rowKey={(r) => `${r.id}-${r.category}-${r.date}`}
                    columns={columnsCallBack}
                    dataSource={data}
                    rowClassName={(record) => {
                        if (record.category === "overdue") return "row-overdue";
                        if (record.category === "yesterday") return "row-yesterday";
                        if (record.category === "upcoming") return "row-upcoming";
                    }}
                    pagination={{
                        current: paginate.current,
                        pageSize: paginate.pageSize,
                        total: paginate.total,
                        showSizeChanger: true,
                        onChange: handlePaginationChange('scheduleCallBack'),
                    }}
                    loading={loading}
                />
            </Card>

            <style>
                {`
                    .row-overdue { background-color: #ffe6e6 !important; color: red; font-weight: 600; }
                    .row-yesterday { background-color: #ffebcc !important; color: black; font-weight: 600; }
                    .row-upcoming { background-color: #24b3305e !important; color: green; font-weight: 600; }
                `}
            </style>
        </>
    );
};
