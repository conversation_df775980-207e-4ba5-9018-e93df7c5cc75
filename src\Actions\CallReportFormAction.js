import * as ActionTypes from "../Constants/CallReportFormConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const postCallReportForm = data => dispatch => {
    dispatch(callReportFormLoading())
    return apiClient.post(`/api/remark`, data)
                    .then((res) => {
                        const msg = res?.data;
                        
                        if(msg?.message) {
                            dispatch(callReportFormSuccess(msg?.message));
                        }
                        else if(msg?.error) {
                            dispatch(callReportFormFailed(msg.error));
                        }
                        
                        return msg;
                    })
                    .catch((err) => {
                        const friendly = handleError(err);
                        dispatch(callReportFormFailed(friendly));
                        throw new Error(friendly);
                    });
}

const callReportFormLoading = () => ({
    type: ActionTypes.CALLREPORTFORM_LOADING
})

const callReportFormSuccess = message => ({
    type: ActionTypes.CALLREPORTFORM_SUCCESS,
    payload: message
})

const callReportFormFailed = err => ({
    type: ActionTypes.CALLREPORTFORM_FAILED,
    payload: err
})