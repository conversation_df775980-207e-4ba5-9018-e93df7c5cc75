import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Col, Drawer, Input, Row, Space, Tag, Card, Modal, Spin, Descriptions } from "antd";
import {
    AudioMutedOutlined,
    BoxPlotOutlined,
    ContactsOutlined,
    CustomerServiceTwoTone,
    LeftSquareOutlined,
    PauseCircleOutlined,
    PhoneOutlined,
    SwapOutlined,
    BellTwoTone,
    UserOutlined,
    CloseOutlined,
} from "@ant-design/icons";
import { TransferModal } from "./SIPModule";
import Timer from "react-compound-timer";
import { SessionState } from "sip.js";
import { useMutation } from "react-query";
import { updateStateOnServer } from "../config/queries";
import apiClient from "../config/apiClient";
import { handleError } from "../components/handleError";
import openNotificationWithIcon from "../components/Notification";
import CampaignWidget from "../components/CampaignWidget";
import initializeEcho from "../config/echo";
import { useSelector } from "react-redux";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrBefore);

const BASE_ALERT_Z_INDEX = 2000;

const CallerDetailsModal = ({ visible, onClose, callerDetails, loading }) => {
    return (
        <Modal
            title={
                <Space>
                    <UserOutlined />
                    Caller Details
                </Space>
            }
            open={visible}
            onCancel={onClose}
            footer={[
                <Button key="close" onClick={onClose} icon={<CloseOutlined />}>
                    Close
                </Button>
            ]}
            width={600}
            centered
        >
            <Spin spinning={loading}>
                {callerDetails ? (
                    <Descriptions bordered column={1} size="small">
                        <Descriptions.Item label="Phone Number">
                            {callerDetails.number || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Name">
                            {callerDetails.name || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Email">
                            {callerDetails.email || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Address">
                            {callerDetails.address || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="City">
                            {callerDetails.city || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Company">
                            {callerDetails.company || 'N/A'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Notes">
                            {callerDetails.notes || 'N/A'}
                        </Descriptions.Item>
                        {callerDetails.last_call_date && (
                            <Descriptions.Item label="Last Call Date">
                                {callerDetails.last_call_date}
                            </Descriptions.Item>
                        )}
                        {callerDetails.call_count && (
                            <Descriptions.Item label="Total Calls">
                                {callerDetails.call_count}
                            </Descriptions.Item>
                        )}
                    </Descriptions>
                ) : (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                        <p>No caller details available</p>
                    </div>
                )}
            </Spin>
        </Modal>
    );
};

const CallbackAlert = ({ onClose, callback, onCallNow, index }) => {
    const when = dayjs.tz(callback.date, "Asia/Karachi").format("YYYY-MM-DD HH:mm");

    return (
        <Card
            style={{
                marginBottom: 16,
                width: 300,
                zIndex: BASE_ALERT_Z_INDEX - index,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            }}
            bodyStyle={{ padding: 16 }}
            title={<Space><BellTwoTone twoToneColor="#1890ff" />Scheduled Callback</Space>}
            extra={<Button size="small" type="text" onClick={() => onClose(callback.id)} >&times;</Button>}
        >
            <div >
                <div>
                    <b>Number:</b> {callback.number}
                </div>
                <div>
                    <b>When:</b> {callback.date} (PKT)
                </div>
                <div>
                    <b>Status:</b> {callback.status}
                </div>
            </div>
            <Space style={{ marginTop: 16, width: '100%', justifyContent: 'flex-end' }}>
                <Button onClick={() => onClose(callback.id)}>
                    Dismiss
                </Button>
                <Button type="primary" onClick={() => onCallNow(callback)}>
                    Call now
                </Button>
            </Space>
        </Card>
    );
};

export default function DialerMenu({
    visible,
    setVisible,
    onClose,
    makeCall,
    authUser,
    setNumber,
    hangupEnable,
    redialClick,
    setRedialClick,
    number,
    outgoindCallId,
    redialId,
    setRedialId,
    showNotification,
    setShowNotification,
    setVarId,
    ...props
}) {
    const userId = sessionStorage.getItem("id");
    const echo = initializeEcho(sessionStorage.getItem("agent_token"));

    const [dialNumber, setDialNumber] = useState("");
    const [numberObject, setNumberObject] = useState(false);
    const [timer] = useState("00:00");
    const [transferVisible, setTransferVisible] = useState(false);
    const [transferType, setTransferType] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showCampaign, setShowCampaign] = useState(false);
    const [activeCampaign, setActiveCampaign] = useState(false);
    const [campaignNumbersData, setCampaignNumbersData] = useState(false);
    const [callStatus, setCallStatus] = useState(false);
    const [waitTimeBeforeCall, setWaitTimeBeforeCall] = useState("");
    const [ratingSettings, setRatingSettings] = useState({ status: 0 });
    const [scheduleCallBack, setScheduleCallBack] = useState([]);

    // Caller Details Modal states
    const [callerDetailsVisible, setCallerDetailsVisible] = useState(false);
    const [callerDetails, setCallerDetails] = useState(null);
    const [callerDetailsLoading, setCallerDetailsLoading] = useState(false);
    const [currentCallNumber, setCurrentCallNumber] = useState(null);

    const outCallState = useSelector((state) => state.OutgoingCallReducer);

    const updateState = useMutation(updateStateOnServer, {
        onSuccess: (data) => openNotificationWithIcon('success', data),
        onError: (error) => openNotificationWithIcon(handleError(error)),
        onSettled: () => {
            setNumberObject(false);
            setCallStatus(false);
            setIsLoading(false);
            setDialNumber("");
        },
    });

    const onCallNow = (cbArg) => {
        setVisible(true);
        setDialNumber(cbArg.number);
        handleDismissCallback(cbArg.id);
        onMakeCall(cbArg.number);
        sessionStorage.setItem("schCallback", true);
        sessionStorage.setItem("current_sch_callback_id", cbArg.id);
    };

    const handleDismissCallback = (callbackId) => {
        setScheduleCallBack(prev => prev.filter(cb => cb.id !== callbackId));
    };

    useEffect(() => {
        if (redialClick) {
            sessionStorage.setItem("schCallback", true);
            sessionStorage.setItem("current_sch_callback_id", redialId);
            setDialNumber(number);
            setNumberObject(number);
            setRedialClick(false);
            onMakeCall(number);
        }
    }, [redialClick, number]);

    const timerRef = useRef();

    const [domainQuery, setDomainQuery] = useState({
        status: "idle",
        isLoading: false,
        isSuccess: false,
        isError: false,
        isIdle: true,
        data: null,
        dataUpdatedAt: 0,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPlaceholderData: false,
        isPreviousData: false,
        isRefetchError: false,
        isStale: false,
    });

    useEffect(() => {
        const channel = echo.private(`agent-panel-systemsetting-channel.${userId}`);

        setDomainQuery((prev) => ({
            ...prev,
            isLoading: true,
            isFetching: true,
            isIdle: false,
        }));

        channel.listen(".agent-panel-systemsetting", (e) => {
            setDomainQuery({
                status: "success",
                isLoading: false,
                isSuccess: true,
                isError: false,
                isIdle: false,
                data: e.data.data[0],
                dataUpdatedAt: Date.now(),
                error: null,
                errorUpdatedAt: 0,
                failureCount: 0,
                errorUpdateCount: 0,
                isFetched: true,
                isFetchedAfterMount: true,
                isFetching: false,
                isRefetching: false,
                isLoadingError: false,
                isPlaceholderData: false,
                isPreviousData: false,
                isRefetchError: false,
                isStale: true,
            });
        });

        return () => {
            channel.stopListening(".agent-panel-systemsetting");
        };
    }, [userId]);

    useEffect(() => {
        apiClient
            .get("/api/campaignSetting")
            .then((res) => setWaitTimeBeforeCall(res.data.wait_seconds_during_calls))
            .catch((error) => openNotificationWithIcon("error", error?.response?.data?.message));
    }, []);

    useEffect(() => {
        if (activeCampaign && !callStatus) {
            apiClient.get(`/api/${activeCampaign.id}/number`).then((r) => {
                if (r.data.number) {
                    setVarId(r.data?.vr_id)
                    setDialNumber(r.data.number);
                    setNumberObject(r.data);
                    setTimeout(() => onMakeCall(r.data.number), waitTimeBeforeCall * 1000 || 5000);
                }
            });
        }
    }, [activeCampaign, callStatus]);

    useEffect(() => {
        switch (transferVisible) {
            case true:
                if (props.isConnected && !props.isBridged) props.holdCall();
                break;
            case false:
                if (props.isConnected && !props.isBridged) props.unholdCall();
                break;
            default:
                break;
        }
    }, [transferVisible]);

    useEffect(() => {
        if (props.isConnected) {
            timerRef.current?.start();
        } else {
            timerRef.current?.reset();
        }
    }, [props.isConnected]);

    useEffect(() => {
        switch (props.sessionState) {
            case SessionState.Establishing:
                setIsLoading(true);
                setCallStatus(true);
                setShowNotification(false);
                break;
            case SessionState.Established:
                // When call is established, fetch caller details if we have a number
                const numberToFetch = dialNumber || number || currentCallNumber;
                if (numberToFetch && activeCampaign) {
                    fetchCallerDetails(numberToFetch);
                }
                break;
            case SessionState.Terminated:
                setIsLoading(false);
                updateStatusOfCall();
                !activeCampaign && setCallStatus(false);

                // Close caller details modal when call is terminated
                closeCallerDetailsModal();

                if (sessionStorage.getItem("schCallback") && sessionStorage.getItem("current_sch_callback_id")) {
                    const schCallBack = JSON.parse(sessionStorage.getItem("schCallback"));
                    const schCallBackId = JSON.parse(sessionStorage.getItem("current_sch_callback_id"));

                    if (schCallBack && schCallBackId && outCallState.outgoingCallId) {
                        updateCallBackStatus(outCallState.outgoingCallId, schCallBackId);
                    }
                }

                break;
            default:
                setIsLoading(false);
                break;
        }
    }, [props.sessionState, dialNumber, number, currentCallNumber, activeCampaign]);

    const updateCallBackStatus = (uniqueId, schCallBackId) => {
        apiClient
            .patch("/api/callback/status", {
                id: schCallBackId,
                agent: userId,
                status: "completed",
                uniqueid: uniqueId,
            })
            .then((response) => {
                if (response?.data?.message === "Status updated successfully") {
                    sessionStorage.setItem("schCallback", false);
                    sessionStorage.setItem("current_sch_callback_id", "");
                }
            })
            .catch((error) => {
                console.error("Error updating callback status:", error);
            });
    };

    // Function to fetch caller details
    const fetchCallerDetails = (phoneNumber) => {
        if (!phoneNumber) return;

        setCallerDetailsLoading(true);
        setCurrentCallNumber(phoneNumber);

        apiClient
            .get(`/${phoneNumber}/number`)
            .then((response) => {
                setCallerDetails(response.data);
                setCallerDetailsVisible(true);
            })
            .catch((error) => {
                console.error("Error fetching caller details:", error);
                openNotificationWithIcon("error", "Failed to fetch caller details");
                // Still show modal even if API fails, but with no data
                setCallerDetails(null);
                setCallerDetailsVisible(true);
            })
            .finally(() => {
                setCallerDetailsLoading(false);
            });
    };

    // Function to close caller details modal
    const closeCallerDetailsModal = () => {
        setCallerDetailsVisible(false);
        setCallerDetails(null);
        setCurrentCallNumber(null);
    };

    const updateStatusOfCall = () => {
        if (activeCampaign) {
            updateState.mutate({
                campaign: activeCampaign.id,
                campaignNumber: numberObject.id,
            });
        }
    };

    const addDigits = (digit) => {
        setDialNumber((d) => d + digit);
        if (props.sessionState === SessionState.Established) {
            props.dtmfInput(digit);
        }
    };

    const removeDigits = () => {
        setDialNumber((d) => d.slice(0, -1));
    };

    const onMakeCall = (num = false) => {
        setIsLoading(true);
        makeCall(num || dialNumber);
    };

    const initiateHold = () => {
        if (props.isHold) props.unholdCall();
        else props.holdCall();
    };

    const initiateMute = () => {
        if (props.isMute) props.unmuteCall();
        else props.muteCall();
    };

    const onTransferCancel = () => setTransferVisible(false);

    const getServiceRatingSettings = () => apiClient.get("/api/getServiceRatingSettings");
    const dispatchServiceRating = (values) => apiClient.post("/api/serviceRating", values);

    const handleServiceRatingClick = () => {
        dispatchServiceRating({ extension: dialNumber, agentId: authUser })
            .then((r) => openNotificationWithIcon('success', r.data))
            .catch((e) => console.log(e));
    };

    useEffect(() => {
        getServiceRatingSettings()
            .then((r) => setRatingSettings(r.data))
            .catch((e) => console.log(e));
    }, []);

    useEffect(() => {
        const channel = echo.private(`agent-panel-schedule-callback-channel.${userId}`);
        channel.listen(".agent-panel-schedule-callback", (e) => {
            const data = Object.values(e.data?.callback || e.data?.data?.callback || e.data || {});
            setScheduleCallBack(data);
            !callStatus && setShowNotification(true);
        });
        return () => {
            channel.stopListening(".agent-panel-schedule-callback");
        };
    }, [userId]);

    return (
        <>
            {showNotification && (
                <div
                    style={{
                        position: 'fixed',
                        top: 24,
                        right: 24,
                        maxHeight: '90vh',
                        overflowY: 'auto',
                        overflowX: 'hidden',
                        zIndex: BASE_ALERT_Z_INDEX,
                        width: 300,
                    }}
                >
                    {scheduleCallBack.map((callback, index) => (
                        <CallbackAlert
                            key={callback.id}
                            callback={callback}
                            onClose={handleDismissCallback}
                            onCallNow={onCallNow}
                            index={index}
                        />
                    ))}
                </div>
            )}

            {/* Caller Details Modal */}
            <CallerDetailsModal
                visible={callerDetailsVisible}
                onClose={closeCallerDetailsModal}
                callerDetails={callerDetails}
                loading={callerDetailsLoading}
            />

            <Drawer title="Dialer" placement="right" closable onClose={onClose} open={visible}>
                <div style={{ marginBottom: 10, border: "1px solid #ccc" }}>
                    <Input
                        allowClear
                        prefix={<PhoneOutlined />}
                        bordered={false}
                        placeholder="Enter number"
                        addonAfter={
                            props.isConnected ? (
                                <Timer
                                    ref={timerRef}
                                    startImmediately={false}
                                    formatValue={(value) => `${value < 10 ? `0${value}` : value}`}
                                >
                                    {() => (
                                        <>
                                            <Timer.Minutes />:<Timer.Seconds />
                                        </>
                                    )}
                                </Timer>
                            ) : (
                                timer
                            )
                        }
                        value={dialNumber}
                        onChange={(e) => {
                            const { value } = e.target;
                            const reg = /^[0-9]*$/;
                            if ((!Number.isNaN(value) && reg.test(value)) || value === "" || value === "-") {
                                setDialNumber(value);
                                setNumber(value);
                            }
                        }}
                    />
                </div>

                <Row justify="space-between" gutter={[10, 10]}>
                    <Col>
                        <Tag color={props.isConnected ? "green" : "red"}>
                            {props.isConnected ? "CONNECTED" : "DISCONNECTED"}
                        </Tag>
                    </Col>
                    <Col>
                        <Button size="small" onClick={removeDigits} type="dashed">
                            <LeftSquareOutlined />
                        </Button>
                    </Col>
                </Row>

                <Row style={{ marginBottom: 10, marginTop: 10 }} gutter={[6, 16]}>
                    <Col span={6}>
                        <Button disabled={!props.isConnected} danger={props.isHold} onClick={initiateHold}>
                            <PauseCircleOutlined />
                        </Button>
                    </Col>
                    <Col span={6}>
                        <Button disabled={!props.isConnected} danger={props.isMute} onClick={initiateMute}>
                            <AudioMutedOutlined />
                        </Button>
                    </Col>
                    <Col span={6}>
                        <Button
                            disabled={!props.isConnected}
                            onClick={() => {
                                setTransferType("blind");
                                setTransferVisible(true);
                            }}
                            title="Transfer"
                        >
                            <SwapOutlined />
                        </Button>
                    </Col>
                    <Col span={6}>
                        <Button
                            onClick={() => {
                                setTransferType("attended");
                                setTransferVisible(true);
                            }}
                            title="Conf"
                        >
                            <ContactsOutlined />
                        </Button>
                    </Col>
                </Row>

                <Row style={{ marginBottom: 10, marginTop: 10 }} justify="center" gutter={[16, 16]}>
                    {["1", "2", "3", "4", "5", "6", "7", "8", "9", "*", "0", "#"].map((value) => (
                        <Col key={value} span={8}>
                            <Button onClick={() => addDigits(value)} block>
                                {value}
                            </Button>
                        </Col>
                    ))}
                </Row>

                <Row style={{ marginBottom: 10, marginTop: 10 }} gutter={[16, 16]}>
                    <Col span={12}>
                        <Button
                            loading={
                                isLoading &&
                                (!sessionStorage.getItem("setDialerSpin") ||
                                    sessionStorage.getItem("setDialerSpin") !== "false")
                            }
                            disabled={props.isConnected || activeCampaign}
                            onClick={() => onMakeCall()}
                            block
                            size="large"
                            icon={<PhoneOutlined />}
                        >
                            Dial
                        </Button>
                    </Col>
                    <Col span={12}>
                        <Button
                            type="primary"
                            danger
                            disabled={domainQuery?.data?.enable_call_hangup == 0}
                            onClick={props.endCall}
                            block
                            size="large"
                        >
                            <svg
                                width="35"
                                height="35"
                                viewBox="0 0 4096.000000 4096.000000"
                                style={{ marginTop: "-5px" }}
                                fill="white"
                            >
                                <g
                                    transform="translate(0.000000,4096.000000) scale(0.100000,-0.100000)"
                                    fill={domainQuery?.data?.enable_call_hangup == 0 ? "#FF8488" : "#fff"}
                                    stroke="none"
                                >
                                    <path d="M20125 28174 c-1537 -25 -3095 -207 -4600 -539 -2389 -528 -4674 -1434 -6790 -2693 -565 -336 -1190 -750 -1744 -1152 -301 -220 -701 -526 -759 -583 -107 -104 -193 -253 -233 -404 -30 -112 -30 -286 0 -408 26 -109 83 -227 150 -315 27 -36 1037 -1051 2243 -2257 2365 -2363 2252 -2255 2426 -2321 114 -44 184 -56 317 -55 152 0 238 22 400 101 189 93 4192 2159 4270 2205 81 47 215 177 270 261 42 66 85 161 112 252 16 54 18 146 23 1173 l5 1115 70 18 c698 177 1678 342 2520 423 593 56 917 70 1680 70 682 0 835 -5 1320 -41 955 -71 2008 -237 2890 -457 l50 -12 5 -1120 5 -1120 33 -95 c70 -201 179 -343 348 -453 110 -71 4370 -2256 4451 -2282 246 -81 514 -44 736 101 57 37 566 541 2370 2343 l2299 2296 -5 250 c-5 279 -15 337 -80 479 -89 192 -161 259 -737 688 -1649 1227 -3470 2235 -5380 2978 -2377 924 -4905 1445 -7465 1539 -296 11 -932 19 -1200 15z m965 -1714 c2172 -69 4168 -427 6207 -1114 1754 -591 3509 -1460 5038 -2494 221 -150 493 -341 499 -351 3 -4 -705 -717 -1572 -1584 l-1577 -1577 -595 306 c-327 168 -671 345 -765 393 -93 48 -554 285 -1022 526 l-853 437 -2 1187 -3 1186 -34 97 c-61 178 -174 332 -316 433 -113 80 -181 106 -505 194 -1381 373 -2746 588 -4200 661 -113 6 -421 13 -685 17 -1313 16 -2571 -95 -3883 -343 -674 -127 -1667 -375 -1839 -459 -203 -100 -356 -276 -434 -503 l-34 -97 -5 -1188 -5 -1188 -1609 -831 -1609 -830 -1581 1581 -1581 1582 25 19 c57 45 380 268 590 407 1827 1214 3802 2124 5925 2732 2051 586 4309 868 6425 801z" />
                                    <path d="M17787 17910 c-268 -45 -499 -212 -621 -450 -63 -122 -87 -211 -93 -356 -6 -141 11 -246 59 -363 68 -163 69 -163 1134 -1231 l1004 -1005 -991 -990 c-544 -545 -1011 -1017 -1036 -1050 -58 -77 -117 -194 -144 -287 -30 -105 -37 -288 -14 -399 99 -490 605 -793 1082 -648 81 25 189 78 258 127 28 20 501 486 1053 1036 l1002 1000 1003 -1000 c551 -550 1025 -1016 1052 -1036 173 -123 397 -183 598 -158 189 23 350 97 484 224 195 184 282 397 270 660 -8 186 -62 339 -170 481 -25 33 -492 505 -1036 1050 l-991 990 1004 1005 c1065 1068 1066 1068 1134 1231 48 117 65 222 59 363 -6 145 -30 234 -93 356 -233 453 -795 594 -1234 312 -34 -22 -439 -418 -1067 -1046 l-1013 -1011 -1012 1011 c-629 628 -1034 1024 -1068 1046 -191 123 -409 172 -613 138z" />
                                </g>
                            </svg>
                        </Button>
                    </Col>
                    {ratingSettings.status !== 0 && (
                        <Col span={24}>
                            <Button
                                onClick={handleServiceRatingClick}
                                disabled={!props.isConnected}
                                size="large"
                                block
                                icon={<CustomerServiceTwoTone />}
                            >
                                Service Rating
                            </Button>
                        </Col>
                    )}
                </Row>

                <Space style={{ marginBottom: 10, marginTop: 10 }} direction="vertical">
                    <div>
                        <Tag icon={<PhoneOutlined />} color="#3b5999">
                            Call status: <b>{props.sessionState}</b>
                        </Tag>
                    </div>
                    <div>
                        <Button shape="round" icon={<BoxPlotOutlined />} onClick={() => setShowCampaign(true)}>
                            Campaign
                        </Button>
                    </div>
                    <div>{activeCampaign && <Tag>Active Campaign: {activeCampaign.name}</Tag>}</div>
                </Space>

                <TransferModal
                    transferType={transferType}
                    visible={transferVisible}
                    onCancel={onTransferCancel}
                    toggleBlindTransfer={props.blindTransfer}
                    toggleAttendedTransfer={props.attendedTransfer}
                    onAcceptTransfer={props.onAcceptTransfer}
                    isConnected={props.isTransferConnected}
                    isHold={props.isTransferHold}
                    isMute={props.isTransferMute}
                    onHold={props.onTransferHold}
                    onUnhold={props.onTransferUnhold}
                    onMute={props.onTransferMute}
                    onUnmute={props.onTransferUnmute}
                    onTransferHangup={props.onTransferHangup}
                    isBridged={props.isBridged}
                    setVisible={setTransferVisible}
                />
                <CampaignWidget
                    showCampaign={showCampaign}
                    setShowCampaign={setShowCampaign}
                    activeCampaign={activeCampaign}
                    setActiveCampaign={setActiveCampaign}
                    campaignNumbersData={campaignNumbersData}
                    user={props.user}
                />
            </Drawer>
        </>
    );
}