// import { DatePicker, Form, Input, Modal } from "antd";
// import { useDispatch, useSelector } from "react-redux";
// import { useEffect } from "react";
// import { postCallReportForm } from "../Actions/CallReportFormAction";
// import { SaveOutlined } from "@ant-design/icons";
// import openNotificationWithIcon from "./Notification";
// import openSuccessNotificationWithIcon from "./Message";
// import { replayNotification } from "../Shared/notification";

// export const CallReportForm = ({ callReportForm, setCallReportForm,setShowNotification }) => {
//     const [form] = Form.useForm();
//     const dispatch = useDispatch();
//     const callReportFormState = useSelector((state) => state.CallReportFormReducer);
//     const { callId } = useSelector((state) => state.CallReducer);
//     const { outgoingCallId } = useSelector((state) => state.OutgoingCallReducer);
//     const userId = sessionStorage.getItem('id');
//     useEffect(() => {
//         if (!callReportFormState.isLoading && callReportFormState.errMess) {
//             openNotificationWithIcon("error", callReportFormState.errMess);
//             dispatch({ type: "RESET_CALLREPORTFORM_MESSAGE" });
//         } else if (!callReportFormState.isLoading && callReportFormState.message) {
//             openSuccessNotificationWithIcon(callReportFormState.message);
//             dispatch({ type: "RESET_CALLREPORTFORM_MESSAGE" });
//         }
//     }, [callReportFormState, dispatch]);

//     useEffect(() => {
//         if (form && callReportForm) {
//             form.setFieldsValue({
//                 unique_id: callId || outgoingCallId,
//             });
//         }
//     }, [callId, outgoingCallId, callReportForm, form]);

//     return (
//         <Modal
//             visible={callReportForm}
//             onCancel={() => setCallReportForm(false)}
//             okText="Submit"
//             title="Call Remark"
//             closable={false}
//             cancelButtonProps={{ style: { display: 'none' } }}
//             maskClosable={false}
//             onOk={() =>
//                 form
//                     .validateFields()
//                     .then((values) => dispatch(postCallReportForm(values)))
//                     .then(() => form.resetFields(["queue", "code"]))
//                     .then(()=>setShowNotification(true))
//                     .then(() => setCallReportForm(false))
//                     .catch((e) => console.log(e))
//             }
//             okButtonProps={{
//                 loading: callReportFormState.isLoading,
//                 icon: <SaveOutlined />,
//             }}
//         >
//             <Form layout={"vertical"} form={form}>
//                 <Form.Item name="agent_id" initialValue={userId} hidden>
//                     <Input type="hidden" />
//                 </Form.Item>

//                 <Form.Item name="unique_id" >
//                     <Input />
//                 </Form.Item>

//                 <Form.Item
//                     label="Remark"
//                     name="remark"
//                     rules={[
//                         {
//                             required: true,
//                             message: 'Notes are required!',
//                         }
//                     ]}
//                 >
//                     <Input.TextArea placeholder="Enter notes here" rows={4} />
//                 </Form.Item>
//             </Form>
//         </Modal>
//     );
// };


import React, { useEffect } from "react";
import { Form, Input, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { SaveOutlined } from "@ant-design/icons";

import { postCallReportForm } from "../Actions/CallReportFormAction";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";

export const CallReportForm = ({
    callReportForm,
    setCallReportForm,
    setShowNotification,
    makeAgentReady,
    varId
}) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();

    const callReportFormState = useSelector((s) => s.CallReportFormReducer);
    const { callId } = useSelector((s) => s.CallReducer);
    const { outgoingCallId } = useSelector((s) => s.OutgoingCallReducer);

    const userId = sessionStorage.getItem("id");

    useEffect(() => {
        if (!callReportFormState.isLoading && callReportFormState.errMess) {
            openNotificationWithIcon("error", callReportFormState.errMess);
            dispatch({ type: "RESET_CALLREPORTFORM_MESSAGE" });
        } else if (!callReportFormState.isLoading && callReportFormState.message) {
            openSuccessNotificationWithIcon(callReportFormState.message);
            dispatch({ type: "RESET_CALLREPORTFORM_MESSAGE" });
        }
    }, [callReportFormState, dispatch]);

    useEffect(() => {
        if (callReportForm) {
            form.setFieldsValue({
                unique_id: callId || outgoingCallId || sessionStorage.getItem("remark_unique_id") || sessionStorage.getItem("call_id") || "",
                agent_id: userId || "",
                var_id: varId || "",
            });
        }
    }, [callId, outgoingCallId, callReportForm, form, userId]);

    const handleFinish = async (values) => {
        try {
            const payload = {
                agent_id: values.agent_id || userId,
                unique_id: values.unique_id || callId || outgoingCallId || sessionStorage.getItem("remark_unique_id") || sessionStorage.getItem("call_id"),
                remark: values.remark,
                var_id: values.var_id || varId,
            };

            const msg = await dispatch(postCallReportForm(payload));
            await makeAgentReady?.();

            sessionStorage.removeItem("remark_required");
            sessionStorage.removeItem("remark_unique_id");

            form.resetFields();
            // setShowNotification?.(true);
            setCallReportForm(false);
        } catch (err) {
            console.error("Call report submit failed:", err);
            openNotificationWithIcon("error", err?.message || "Failed to submit call remark");
            form.resetFields();
        }
    };

    const handleCancel = () => {
        if (!callReportFormState.isLoading) {
            form.resetFields();
            setCallReportForm(false);
        };
    };

    return (
        <Modal
            visible={callReportForm}
            onCancel={handleCancel}
            okText="Submit"
            title="Call Remark"
            closable={false}
            cancelButtonProps={{ style: { display: "none" } }}
            maskClosable={false}
            onOk={() => form.submit()}
            okButtonProps={{
                loading: callReportFormState.isLoading,
                icon: <SaveOutlined />,
            }}
        >
            <Form
                layout="vertical"
                form={form}
                onFinish={handleFinish}
            >
                <Form.Item name="agent_id" initialValue={userId} hidden>
                    <Input type="hidden" />
                </Form.Item>


                {varId && <Form.Item name="var_id" initialValue={varId} hidden>
                    <Input type="hidden" />
                </Form.Item>}

                <Form.Item label="Unique ID" name="unique_id">
                    <Input disabled />
                </Form.Item>

                <Form.Item
                    label="Remark"
                    name="remark"
                    rules={[
                        { required: true, message: "Notes are required!" },
                        { min: 2, message: "Please enter at least 2 characters." },
                    ]}
                >
                    <Input.TextArea placeholder="Enter notes here" rows={4} />
                </Form.Item>
            </Form>
        </Modal>
    );
};
