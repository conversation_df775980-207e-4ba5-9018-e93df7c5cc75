import { DatePicker, Form, Input, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { postCallReminder } from "../Actions/CallReminderAction";
import { SaveOutlined } from "@ant-design/icons";
import openNotificationWithIcon from "./Notification";
import openSuccessNotificationWithIcon from "./Message";
import { replayNotification } from "../Shared/notification";
import apiClient from "../config/apiClient";

export const CallReminder = ({ callReminder, setCallReminder, setCallReportForm }) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const callReminderState = useSelector((state) => state.CallReminderReducer);
    const { callId } = useSelector((state) => state.CallReducer);
    const { outgoingCallId } = useSelector((state) => state.OutgoingCallReducer);
    const userId = sessionStorage.getItem('id');
    useEffect(() => {
        if (!callReminderState.isLoading && callReminderState.errMess) {
            openNotificationWithIcon("error", callReminderState.errMess);
            dispatch({ type: "RESET_CALLREMINDER_MESSAGE" });
        } else if (!callReminderState.isLoading && callReminderState.message) {
            openSuccessNotificationWithIcon(callReminderState.message);
            dispatch({ type: "RESET_CALLREMINDER_MESSAGE" });
        }
    }, [callReminderState, dispatch]);

    useEffect(() => {
        if (form && callReminder) {
            form.setFieldsValue({
                channel: callId || outgoingCallId,
            });

            if (callId || outgoingCallId) {
                sessionStorage.setItem("remark_required", "1");
                sessionStorage.setItem("remark_unique_id", callId || outgoingCallId);
            }
        }
    }, [callId, outgoingCallId, callReminder, form]);

    return (
        <Modal
            visible={callReminder}
            onCancel={() => {
                setCallReminder(false)
                setCallReportForm(true)
            }}
            okText="Submit"
            title="Schedule Call"
            closable={false}
            // cancelButtonProps={{ style: { display: 'none' } }}
            // onOk={() =>
            //     form
            //         .validateFields()
            //         .then((values) => { 
            //             const payload = { ...values, date: values.date.format("YYYY-MM-DD HH:mm:ss"), created_by: parseInt(userId, 10) };
            //             //  dispatch(postCallReminder(payload)) 
            //              apiClient.post('/api/callback',payload).then((res)=>{
            //                 openNotificationWithIcon('success',res.data?.message)
            //                 setCallReportForm(true)
            //              }).catch(()=>{
            //                 openNotificationWithIcon('error',"Something went wrong!")
            //                 setCallReportForm(true)
            //              })
            //         })
            //         .then(() => form.resetFields(["queue", "code"]))
            //         .then(() => setCallReminder(false))
            //         .catch((e) => console.log(e))
            // }
            onOk={() =>
                form.validateFields()
                    .then((values) => {
                        const payload = {
                            ...values,
                            date: values.date.format("YYYY-MM-DD HH:mm:ss"),
                            created_by: parseInt(userId, 10),
                        };

                        return apiClient.post('/api/callback', payload);
                    })
                    .then((res) => {
                        const { data } = res;
                        if (data?.message) {
                            openNotificationWithIcon('success', data.message);

                            form.resetFields(['queue', 'code']);
                            setCallReportForm(true);
                            setCallReminder(false);

                        } else if (data?.error) {
                            openNotificationWithIcon('error', data.error);
                        }
                    })
                    .catch((err) => {
                        const status = err?.response?.status;
                        const serverMsg =
                            err?.response?.data?.message ||
                            err?.response?.data?.error ||
                            err?.message ||
                            'Something went wrong!';

                        if (status === 409) {
                            openNotificationWithIcon('warning', serverMsg);
                        } else {
                            openNotificationWithIcon('error', serverMsg);
                        }
                    })
            }
            okButtonProps={{
                loading: callReminderState.isLoading,
                icon: <SaveOutlined />,
            }}
        >
            <Form layout={"vertical"} form={form}>
                <Form.Item noStyle
                    name="uniqueid"
                >
                    <Input type="hidden" />
                </Form.Item>
                <Form.Item
                    label="Customer Number"
                    name="number"
                    rules={[
                        {
                            required: true,
                            message: 'Customer number is required!',
                        },
                        {
                            pattern: /^\d+$/,
                            message: 'Customer number must be a number!',
                        }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    label="Date & Time"
                    name="date"
                    rules={[
                        {
                            required: true,
                            message: 'Duration is required!',
                        }
                    ]}
                >
                    <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" style={{ width: "100%" }} />
                </Form.Item>
            </Form>
        </Modal>
    );
};